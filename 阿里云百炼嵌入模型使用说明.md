# 阿里云百炼嵌入模型使用说明

## 概述

本项目现已支持阿里云百炼（DashScope）的嵌入模型服务。阿里云百炼提供了高质量的中文嵌入模型，特别适合中文文本的向量化处理。

## 配置步骤

### 1. 获取API Key

1. 访问 [阿里云百炼控制台](https://dashscope.aliyun.com/)
2. 注册并登录阿里云账号
3. 开通百炼服务
4. 在控制台中获取API Key

### 2. 在应用中配置

1. 打开小说生成器应用
2. 切换到"配置"选项卡
3. 选择"嵌入模型设置"子选项卡
4. 在"Embedding 接口格式"下拉菜单中选择"阿里云百炼"
5. 填入以下配置信息：
   - **Embedding API Key**: 您从阿里云百炼获取的API Key
   - **Embedding Base URL**: `https://dashscope.aliyuncs.com/compatible-mode/v1` (自动填入)
   - **Embedding Model Name**: `text-embedding-v4` (自动填入，推荐使用)

### 3. 可选模型

阿里云百炼支持多种嵌入模型，您可以根据需要选择：

- **text-embedding-v4** (推荐)
  - 向量维度：2048、1536、1024（默认）、768、512、256、128、64
  - 支持100+主流语种
  - 单行最大处理Token数：8,192

- **text-embedding-v3**
  - 向量维度：1024（默认）、768、512、256、128、64
  - 支持50+主流语种
  - 单行最大处理Token数：8,192

- **text-embedding-v2**
  - 向量维度：1536
  - 支持中文、英语等多种语言
  - 单行最大处理Token数：2,048

- **text-embedding-v1**
  - 向量维度：1536
  - 支持中文、英语等语言

## 使用特点

### 优势
- **中文优化**: 专门针对中文文本进行优化，效果优秀
- **高质量**: 基于Qwen3-Embedding系列，质量可靠
- **多维度支持**: 支持多种向量维度选择
- **OpenAI兼容**: 使用OpenAI兼容接口，集成简单

### 计费说明
- 按Token数量计费
- 具体价格请参考阿里云百炼官方定价

## 测试配置

配置完成后，可以点击"测试Embedding配置"按钮来验证配置是否正确。成功的测试会显示：
- ✅ Embedding配置测试成功！
- 生成的向量维度信息

## 故障排除

### 常见问题

1. **API Key错误**
   - 确认API Key是否正确复制
   - 检查API Key是否已激活

2. **网络连接问题**
   - 确认网络连接正常
   - 检查防火墙设置

3. **配额限制**
   - 检查阿里云账户余额
   - 确认API调用配额

### 错误代码说明

- `400 InvalidParameter`: 参数错误，检查模型名称和输入格式
- `401 Unauthorized`: API Key无效或未授权
- `429 Too Many Requests`: 请求频率过高，请稍后重试
- `500 Internal Server Error`: 服务器内部错误，请稍后重试

## 技术实现

本项目通过 `DashScopeEmbeddingAdapter` 类实现对阿里云百炼嵌入模型的支持：

- 使用HTTP POST请求调用阿里云百炼的OpenAI兼容接口
- 支持单个文本和批量文本的嵌入处理
- 自动处理错误和重试机制
- 与项目的向量存储系统无缝集成

## 更新日志

- **2025-06-26**: 添加阿里云百炼嵌入模型支持
  - 新增 `DashScopeEmbeddingAdapter` 适配器
  - 在UI界面添加"阿里云百炼"选项
  - 支持 text-embedding-v4 等多种模型

---

如有问题，请参考阿里云百炼官方文档或联系技术支持。
