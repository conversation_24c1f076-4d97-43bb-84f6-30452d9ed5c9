# -*- mode: python ; coding: utf-8 -*-
from PyInstaller.utils.hooks import collect_all
import os
import sys

datas = []
binaries = []
hiddenimports = ['typing_extensions',
                 'langchain_openai',
                 'langchain_core',
                 'langchain_community',
                 'langchain_chroma',
                 'openai',
                 'google.generativeai',
                 'google',
                 'nltk',
                 'sentence_transformers',
                 'scikit_learn',
                 'pydantic',
                 'pydantic.deprecated.decorator',
                 'tiktoken_ext.openai_public',
                 'tiktoken_ext',
                 'chromadb.utils.embedding_functions.onnx_mini_lm_l6_v2',
                 'azure.ai.inference',
                 'azure.core',
                 'customtkinter',
                 'darkdetect'
                 ]

# 收集chromadb相关文件
try:
    tmp_ret = collect_all('chromadb')
    datas += tmp_ret[0]; binaries += tmp_ret[1]; hiddenimports += tmp_ret[2]
except:
    pass

# 自动查找customtkinter路径
try:
    import customtkinter
    customtkinter_dir = os.path.dirname(customtkinter.__file__)
    datas.append((customtkinter_dir, 'customtkinter'))
except:
    pass

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='小说生成器',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico'
)

coll = COLLECT(
    exe,
    a.binaries,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='小说生成器'
)
