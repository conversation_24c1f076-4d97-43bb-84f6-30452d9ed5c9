# 小说生成器简易安装包创建脚本
# 使用PowerShell和7-Zip创建自解压安装包

param(
    [string]$OutputPath = "小说生成器安装包.exe"
)

Write-Host "正在创建小说生成器安装包..." -ForegroundColor Green
Write-Host ""

# 检查必要文件
$appPath = "dist\小说生成器"
if (-not (Test-Path $appPath)) {
    Write-Host "错误：未找到打包后的程序文件夹：$appPath" -ForegroundColor Red
    Write-Host "请先运行 PyInstaller 打包程序" -ForegroundColor Yellow
    exit 1
}

if (-not (Test-Path "$appPath\小说生成器.exe")) {
    Write-Host "错误：未找到主程序文件：$appPath\小说生成器.exe" -ForegroundColor Red
    exit 1
}

# 创建临时安装脚本
$installScript = @"
@echo off
chcp 65001 >nul
echo 正在安装小说生成器...
echo.

set "INSTALL_DIR=%ProgramFiles%\小说生成器"

echo 创建安装目录...
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

echo 复制程序文件...
xcopy /E /I /Y "小说生成器\*" "%INSTALL_DIR%\"

echo 创建桌面快捷方式...
powershell -Command "& {`$WshShell = New-Object -comObject WScript.Shell; `$Shortcut = `$WshShell.CreateShortcut('%USERPROFILE%\Desktop\小说生成器.lnk'); `$Shortcut.TargetPath = '%INSTALL_DIR%\小说生成器.exe'; `$Shortcut.Save()}"

echo 创建开始菜单快捷方式...
if not exist "%ProgramData%\Microsoft\Windows\Start Menu\Programs\小说生成器" mkdir "%ProgramData%\Microsoft\Windows\Start Menu\Programs\小说生成器"
powershell -Command "& {`$WshShell = New-Object -comObject WScript.Shell; `$Shortcut = `$WshShell.CreateShortcut('%ProgramData%\Microsoft\Windows\Start Menu\Programs\小说生成器\小说生成器.lnk'); `$Shortcut.TargetPath = '%INSTALL_DIR%\小说生成器.exe'; `$Shortcut.Save()}"

echo.
echo 安装完成！
echo 程序已安装到：%INSTALL_DIR%
echo 您可以在桌面或开始菜单找到小说生成器的快捷方式。
echo.
pause

REM 清理临时文件
cd /d "%TEMP%"
rmdir /s /q "小说生成器_安装临时文件" 2>nul
"@

# 创建临时目录
$tempDir = Join-Path $env:TEMP "小说生成器_打包临时"
if (Test-Path $tempDir) {
    Remove-Item $tempDir -Recurse -Force
}
New-Item -ItemType Directory -Path $tempDir -Force | Out-Null

# 复制程序文件到临时目录
Write-Host "复制程序文件..." -ForegroundColor Yellow
Copy-Item -Path $appPath -Destination $tempDir -Recurse -Force

# 创建安装脚本文件
$installScriptPath = Join-Path $tempDir "install.bat"
$installScript | Out-File -FilePath $installScriptPath -Encoding UTF8

# 创建README文件
$readmeContent = @"
小说生成器 v1.4.2

安装说明：
1. 双击 install.bat 开始安装
2. 安装程序会将文件复制到 Program Files\小说生成器
3. 自动创建桌面和开始菜单快捷方式

系统要求：
- Windows 10 或更高版本
- 至少 2GB 可用磁盘空间

使用说明：
- 首次运行需要配置AI模型设置
- 支持OpenAI、Google Gemini、Azure等多种AI服务
- 详细使用说明请参考软件内的帮助文档

如有问题，请联系开发者。
"@

$readmePath = Join-Path $tempDir "README.txt"
$readmeContent | Out-File -FilePath $readmePath -Encoding UTF8

Write-Host "创建压缩包..." -ForegroundColor Yellow

# 使用PowerShell内置的压缩功能创建ZIP文件
$zipPath = "小说生成器_便携版.zip"
if (Test-Path $zipPath) {
    Remove-Item $zipPath -Force
}

Add-Type -AssemblyName System.IO.Compression.FileSystem
[System.IO.Compression.ZipFile]::CreateFromDirectory($tempDir, $zipPath)

# 清理临时文件
Remove-Item $tempDir -Recurse -Force

Write-Host ""
Write-Host "✓ 安装包创建成功！" -ForegroundColor Green
Write-Host "文件位置：$zipPath" -ForegroundColor Cyan
Write-Host ""
Write-Host "使用说明：" -ForegroundColor Yellow
Write-Host "1. 将 $zipPath 发送给用户" -ForegroundColor White
Write-Host "2. 用户解压后运行 install.bat 即可安装" -ForegroundColor White
Write-Host "3. 或者用户可以直接运行解压后的程序（便携版）" -ForegroundColor White
Write-Host ""
Write-Host "按任意键继续..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
