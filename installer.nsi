# 小说生成器安装脚本
# 使用NSIS创建Windows安装包

!define APPNAME "小说生成器"
!define COMPANYNAME "AI Novel Generator"
!define DESCRIPTION "基于AI的智能小说生成工具"
!define VERSIONMAJOR 1
!define VERSIONMINOR 4
!define VERSIONBUILD 2
!define HELPURL "https://github.com/your-repo/ai-novel-generator"
!define UPDATEURL "https://github.com/your-repo/ai-novel-generator/releases"
!define ABOUTURL "https://github.com/your-repo/ai-novel-generator"
!define INSTALLSIZE 150000

RequestExecutionLevel admin
InstallDir "$PROGRAMFILES\${APPNAME}"
LicenseData "LICENSE"
Name "${APPNAME}"
Icon "icon.ico"
outFile "小说生成器安装包.exe"

!include LogicLib.nsh

page license
page directory
page instfiles

!macro VerifyUserIsAdmin
UserInfo::GetAccountType
pop $0
${If} $0 != "admin"
    messageBox mb_iconstop "需要管理员权限才能安装此程序。"
    setErrorLevel 740
    quit
${EndIf}
!macroend

function .onInit
    setShellVarContext all
    !insertmacro VerifyUserIsAdmin
functionEnd

section "install"
    # 设置输出路径
    setOutPath $INSTDIR
    
    # 复制主程序文件
    file "dist\小说生成器\小说生成器.exe"
    
    # 复制_internal目录及其所有内容
    file /r "dist\小说生成器\_internal"
    
    # 复制图标文件（如果存在）
    IfFileExists "icon.ico" 0 +2
    file "icon.ico"
    
    # 创建开始菜单快捷方式
    createDirectory "$SMPROGRAMS\${APPNAME}"
    createShortCut "$SMPROGRAMS\${APPNAME}\${APPNAME}.lnk" "$INSTDIR\小说生成器.exe" "" "$INSTDIR\icon.ico"
    createShortCut "$SMPROGRAMS\${APPNAME}\卸载.lnk" "$INSTDIR\uninstall.exe"
    
    # 创建桌面快捷方式
    createShortCut "$DESKTOP\${APPNAME}.lnk" "$INSTDIR\小说生成器.exe" "" "$INSTDIR\icon.ico"
    
    # 写入卸载程序
    writeUninstaller "$INSTDIR\uninstall.exe"
    
    # 写入注册表信息
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "DisplayName" "${APPNAME}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "UninstallString" "$\"$INSTDIR\uninstall.exe$\""
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "QuietUninstallString" "$\"$INSTDIR\uninstall.exe$\" /S"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "InstallLocation" "$\"$INSTDIR$\""
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "DisplayIcon" "$\"$INSTDIR\icon.ico$\""
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "Publisher" "${COMPANYNAME}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "HelpLink" "${HELPURL}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "URLUpdateInfo" "${UPDATEURL}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "URLInfoAbout" "${ABOUTURL}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "DisplayVersion" "${VERSIONMAJOR}.${VERSIONMINOR}.${VERSIONBUILD}"
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "VersionMajor" ${VERSIONMAJOR}
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "VersionMinor" ${VERSIONMINOR}
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "NoModify" 1
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "NoRepair" 1
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}" "EstimatedSize" ${INSTALLSIZE}
    
    # 显示安装完成消息
    MessageBox MB_OK "安装完成！您可以在开始菜单或桌面找到${APPNAME}的快捷方式。"
sectionEnd

# 卸载程序
function un.onInit
    SetShellVarContext all
    MessageBox MB_OKCANCEL|MB_ICONQUESTION "确定要完全卸载 ${APPNAME} 及其所有组件吗？" IDOK next
        Abort
    next:
    !insertmacro VerifyUserIsAdmin
functionEnd

section "uninstall"
    # 删除程序文件
    delete "$INSTDIR\小说生成器.exe"
    delete "$INSTDIR\icon.ico"
    delete "$INSTDIR\uninstall.exe"
    
    # 删除_internal目录
    rmDir /r "$INSTDIR\_internal"
    
    # 删除快捷方式
    delete "$SMPROGRAMS\${APPNAME}\${APPNAME}.lnk"
    delete "$SMPROGRAMS\${APPNAME}\卸载.lnk"
    rmDir "$SMPROGRAMS\${APPNAME}"
    delete "$DESKTOP\${APPNAME}.lnk"
    
    # 删除注册表项
    DeleteRegKey HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APPNAME}"
    
    # 删除安装目录
    rmDir "$INSTDIR"
    
    MessageBox MB_OK "卸载完成！"
sectionEnd
