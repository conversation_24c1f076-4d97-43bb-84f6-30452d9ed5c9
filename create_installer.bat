@echo off
chcp 65001 >nul
echo 正在创建小说生成器Windows安装包...
echo.

REM 检查NSIS是否已安装
where makensis >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误：未找到NSIS。请先安装NSIS（Nullsoft Scriptable Install System）
    echo 下载地址：https://nsis.sourceforge.io/Download
    echo.
    echo 安装NSIS后，请将NSIS安装目录添加到系统PATH环境变量中
    echo 通常NSIS安装在：C:\Program Files (x86)\NSIS\
    pause
    exit /b 1
)

REM 检查必要文件是否存在
if not exist "dist\小说生成器\小说生成器.exe" (
    echo 错误：未找到打包后的程序文件
    echo 请先运行 PyInstaller 打包程序
    pause
    exit /b 1
)

if not exist "installer.nsi" (
    echo 错误：未找到安装脚本文件 installer.nsi
    pause
    exit /b 1
)

echo 开始编译安装包...
makensis installer.nsi

if %errorlevel% equ 0 (
    echo.
    echo ✓ 安装包创建成功！
    echo 文件位置：小说生成器安装包.exe
    echo.
    echo 您现在可以将此安装包分发给其他用户了。
) else (
    echo.
    echo ✗ 安装包创建失败！
    echo 请检查错误信息并重试。
)

echo.
pause
